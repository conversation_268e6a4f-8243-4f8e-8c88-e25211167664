import type { ReactNode } from "react";
import { useUserToken } from "@/hooks/use-user";
import { useNavigate } from 'react-router'
import { useEffect } from "react";
type AuthRoutProps = {
    children: ReactNode
}

export function AuthRoute({ children }: AuthRoutProps) {
    const { userToken } = useUserToken()
    const navigate = useNavigate()

    useEffect(() => {
        if (!userToken.token) {
            navigate('/login')
        }
    }, [userToken.token, navigate])

    // 如果没有token，不渲染子组件
    if (!userToken.token) {
        return null
    }

    return <>{children}</>

}