import fs from 'fs'
import path from 'path'

export default function apiCollectorPlugin() {
  return {
    name: 'api-collector',
    buildStart() {
      const projectRoot = process.cwd()
      const apiDir = path.join(projectRoot, 'src/apis')
      
      if (!fs.existsSync(apiDir)) {
        console.warn(`API directory not found: ${apiDir}`)
        return
      }
      
      const files = fs.readdirSync(apiDir)
      
      let content = '// Auto-generated by vite plugin\n\nexport default {\n'
      
      files.forEach(file => {
        if (file.endsWith('.ts') && file !== '_all.ts') {
          content += `  ...require('./${file}').default,\n`
        }
      })
      
      content += '}\n'
      
      fs.writeFileSync(path.join(apiDir, '_all.ts'), content)
    }
  }
}