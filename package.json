{"name": "sakura-react-admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode development", "production": "vite --mode production", "build": "tsc -b && vite build --mode production", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/v5-patch-for-react-19": "^1.0.3", "@hookform/resolvers": "^5.2.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/vite": "^4.1.11", "@tanstack/react-query": "^5.83.0", "@tanstack/react-table": "^8.21.3", "@types/mockjs": "^1.0.10", "antd": "^5.26.6", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.526.0", "next-themes": "^0.4.6", "ramda": "^0.31.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.61.1", "react-router": "^7.7.1", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "typescript-plugin-css-modules": "^5.2.0", "zod": "^4.0.10", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.30.1", "@faker-js/faker": "^9.9.0", "@iconify/react": "^6.0.0", "@types/node": "^24.1.0", "@types/ramda": "^0.31.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "mockjs": "^1.1.0", "sass": "^1.89.2", "tw-animate-css": "^1.3.6", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4", "vite-plugin-mock": "^3.0.2"}}